<?php
/**
 * AI服务数据库设置脚本
 * 用于初始化AI服务相关的数据库表和默认数据
 * 
 * @version 1.0.0
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义API访问常量
define('API_ACCESS', true);

require_once __DIR__ . '/config.php';

class AiServiceSetup {
    private $db;
    private $config;
    
    public function __construct() {
        $this->config = new ApiConfig();
        $this->db = $this->config->getDatabaseConnection();
    }
    
    /**
     * 运行设置
     */
    public function runSetup() {
        echo "<h1>AI服务数据库设置</h1>\n";
        echo "<p>设置时间: " . date('Y-m-d H:i:s') . "</p>\n";
        
        try {
            // 创建表
            $this->createTables();
            
            // 插入默认数据
            $this->insertDefaultData();
            
            // 验证设置
            $this->verifySetup();
            
            echo "<h2>✅ 设置完成</h2>\n";
            echo "<p>AI服务数据库已成功设置。</p>\n";
            
        } catch (Exception $e) {
            echo "<h2>❌ 设置失败</h2>\n";
            echo "<p>错误: " . $e->getMessage() . "</p>\n";
        }
    }
    
    /**
     * 创建表
     */
    private function createTables() {
        echo "<h2>创建数据库表</h2>\n";
        
        // AI服务配置表
        $this->createAiServiceConfigsTable();
        
        // AI服务API密钥表
        $this->createAiServiceApiKeysTable();
        
        // AI服务日志表
        $this->createAiServiceLogsTable();
    }
    
    /**
     * 创建AI服务配置表
     */
    private function createAiServiceConfigsTable() {
        $sql = "
        CREATE TABLE IF NOT EXISTS `ai_service_configs` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `service_name` varchar(50) NOT NULL COMMENT '服务名称 (deepseek, doubao)',
          `service_type` varchar(50) NOT NULL COMMENT '服务类型 (chat, completion)',
          `is_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
          `api_endpoint` varchar(255) NOT NULL COMMENT 'API端点URL',
          `model_name` varchar(100) NOT NULL COMMENT '模型名称',
          `system_prompt` text COMMENT '系统提示词',
          `reply_delay` int(11) NOT NULL DEFAULT 0 COMMENT '回复延迟（秒）',
          `thinking_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用深度思考',
          `max_tokens` int(11) NOT NULL DEFAULT 4000 COMMENT '最大令牌数',
          `temperature` decimal(3,2) NOT NULL DEFAULT 0.70 COMMENT '温度参数',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `service_name` (`service_name`),
          KEY `idx_enabled` (`is_enabled`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务配置表'
        ";
        
        $this->db->exec($sql);
        echo "<p>✅ AI服务配置表创建成功</p>\n";
    }
    
    /**
     * 创建AI服务API密钥表
     */
    private function createAiServiceApiKeysTable() {
        $sql = "
        CREATE TABLE IF NOT EXISTS `ai_service_api_keys` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `service_name` varchar(50) NOT NULL COMMENT '服务名称 (deepseek, doubao)',
          `api_key` varchar(255) NOT NULL COMMENT 'API密钥（加密存储）',
          `api_key_hash` varchar(64) NOT NULL COMMENT 'API密钥哈希值（用于验证）',
          `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
          `is_valid` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否验证有效',
          `last_validated_at` timestamp NULL DEFAULT NULL COMMENT '最后验证时间',
          `validation_error` text COMMENT '验证错误信息',
          `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
          `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `api_key_hash` (`api_key_hash`),
          KEY `idx_service_name` (`service_name`),
          KEY `idx_active_valid` (`is_active`, `is_valid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务API密钥表'
        ";
        
        $this->db->exec($sql);
        echo "<p>✅ AI服务API密钥表创建成功</p>\n";
        
        // 添加外键约束（如果不存在）
        try {
            $this->db->exec("
                ALTER TABLE `ai_service_api_keys` 
                ADD CONSTRAINT `fk_api_keys_service` 
                FOREIGN KEY (`service_name`) REFERENCES `ai_service_configs`(`service_name`) 
                ON DELETE CASCADE
            ");
        } catch (PDOException $e) {
            // 外键可能已存在，忽略错误
        }
    }
    
    /**
     * 创建AI服务日志表
     */
    private function createAiServiceLogsTable() {
        $sql = "
        CREATE TABLE IF NOT EXISTS `ai_service_logs` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `service_name` varchar(50) NOT NULL COMMENT '服务名称',
          `api_key_id` int(11) DEFAULT NULL COMMENT 'API密钥ID',
          `action` varchar(50) NOT NULL COMMENT '操作类型 (validate, chat, test)',
          `request_data` text COMMENT '请求数据（JSON）',
          `response_data` text COMMENT '响应数据（JSON）',
          `status` varchar(20) NOT NULL COMMENT '状态 (success, error)',
          `error_message` text COMMENT '错误信息',
          `response_time` int(11) DEFAULT NULL COMMENT '响应时间（毫秒）',
          `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
          `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_service_name` (`service_name`),
          KEY `idx_api_key_id` (`api_key_id`),
          KEY `idx_status` (`status`),
          KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务使用日志表'
        ";
        
        $this->db->exec($sql);
        echo "<p>✅ AI服务日志表创建成功</p>\n";
        
        // 添加外键约束（如果不存在）
        try {
            $this->db->exec("
                ALTER TABLE `ai_service_logs` 
                ADD CONSTRAINT `fk_logs_service` 
                FOREIGN KEY (`service_name`) REFERENCES `ai_service_configs`(`service_name`) 
                ON DELETE CASCADE
            ");
            
            $this->db->exec("
                ALTER TABLE `ai_service_logs` 
                ADD CONSTRAINT `fk_logs_api_key` 
                FOREIGN KEY (`api_key_id`) REFERENCES `ai_service_api_keys`(`id`) 
                ON DELETE SET NULL
            ");
        } catch (PDOException $e) {
            // 外键可能已存在，忽略错误
        }
    }
    
    /**
     * 插入默认数据
     */
    private function insertDefaultData() {
        echo "<h2>插入默认数据</h2>\n";
        
        // 插入DeepSeek配置
        $this->insertServiceConfig(
            'deepseek',
            'chat',
            'https://api.deepseek.com/chat/completions',
            'deepseek-chat',
            '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
        );
        
        // 插入Doubao配置
        $this->insertServiceConfig(
            'doubao',
            'chat',
            'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
            'doubao-seed-1-6-250615',
            '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。'
        );
    }
    
    /**
     * 插入服务配置
     */
    private function insertServiceConfig($serviceName, $serviceType, $apiEndpoint, $modelName, $systemPrompt) {
        $sql = "
        INSERT INTO `ai_service_configs` 
        (`service_name`, `service_type`, `is_enabled`, `api_endpoint`, `model_name`, `system_prompt`, `reply_delay`, `thinking_enabled`, `max_tokens`, `temperature`) 
        VALUES (?, ?, 0, ?, ?, ?, 0, 0, 4000, 0.70)
        ON DUPLICATE KEY UPDATE 
          `api_endpoint` = VALUES(`api_endpoint`),
          `model_name` = VALUES(`model_name`),
          `system_prompt` = VALUES(`system_prompt`),
          `updated_at` = CURRENT_TIMESTAMP
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$serviceName, $serviceType, $apiEndpoint, $modelName, $systemPrompt]);
        
        echo "<p>✅ {$serviceName} 服务配置插入成功</p>\n";
    }
    
    /**
     * 验证设置
     */
    private function verifySetup() {
        echo "<h2>验证设置</h2>\n";
        
        // 检查表是否存在
        $tables = ['ai_service_configs', 'ai_service_api_keys', 'ai_service_logs'];
        
        foreach ($tables as $table) {
            $stmt = $this->db->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            
            if ($stmt->rowCount() > 0) {
                echo "<p>✅ 表 {$table} 存在</p>\n";
            } else {
                throw new Exception("表 {$table} 不存在");
            }
        }
        
        // 检查默认配置
        $stmt = $this->db->query("SELECT COUNT(*) FROM ai_service_configs");
        $configCount = $stmt->fetchColumn();
        
        if ($configCount >= 2) {
            echo "<p>✅ 默认服务配置已插入 ({$configCount} 条记录)</p>\n";
        } else {
            throw new Exception("默认服务配置插入失败");
        }
        
        // 检查服务配置详情
        $stmt = $this->db->query("SELECT service_name, is_enabled, model_name FROM ai_service_configs ORDER BY service_name");
        $configs = $stmt->fetchAll();
        
        echo "<h3>服务配置详情:</h3>\n";
        foreach ($configs as $config) {
            $status = $config['is_enabled'] ? '启用' : '禁用';
            echo "<p>• {$config['service_name']}: {$config['model_name']} ({$status})</p>\n";
        }
    }
    
    /**
     * 清理设置（用于重置）
     */
    public function cleanupSetup() {
        echo "<h1>清理AI服务数据库</h1>\n";
        
        try {
            // 删除表（按依赖关系顺序）
            $this->db->exec("DROP TABLE IF EXISTS `ai_service_logs`");
            $this->db->exec("DROP TABLE IF EXISTS `ai_service_api_keys`");
            $this->db->exec("DROP TABLE IF EXISTS `ai_service_configs`");
            
            echo "<p>✅ 所有AI服务表已删除</p>\n";
            
        } catch (Exception $e) {
            echo "<p>❌ 清理失败: " . $e->getMessage() . "</p>\n";
        }
    }
}

// 处理请求
if (isset($_GET['action'])) {
    $setup = new AiServiceSetup();
    
    switch ($_GET['action']) {
        case 'setup':
            $setup->runSetup();
            break;
        case 'cleanup':
            $setup->cleanupSetup();
            break;
        default:
            echo "<p>无效的操作</p>\n";
    }
} else {
    echo "<h1>AI服务数据库设置</h1>\n";
    echo "<p><a href='?action=setup'>运行设置</a></p>\n";
    echo "<p><a href='?action=cleanup'>清理设置</a></p>\n";
}
?>
