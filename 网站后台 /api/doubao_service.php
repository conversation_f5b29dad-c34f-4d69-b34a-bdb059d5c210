<?php
/**
 * <PERSON>ubao (豆包) AI服务API接口
 * 提供Doubao AI服务的完整功能，包括配置管理、API密钥管理、验证等
 * 
 * @version 1.0.0
 */

// 定义API访问常量
define('API_ACCESS', true);

require_once __DIR__ . '/ai_service_base.php';

class DoubaoService extends AiServiceBase {
    
    public function __construct() {
        parent::__construct('doubao');
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            $action = $this->request['action'] ?? '';
            
            switch ($this->method) {
                case 'GET':
                    $this->handleGetRequest($action);
                    break;
                case 'POST':
                    $this->handlePostRequest($action);
                    break;
                case 'PUT':
                    $this->handlePutRequest($action);
                    break;
                case 'DELETE':
                    $this->handleDeleteRequest($action);
                    break;
                default:
                    $this->respondError('不支持的请求方法', 405);
            }
        } catch (Exception $e) {
            error_log("Doubao服务错误: " . $e->getMessage());
            $this->respondError('服务器内部错误', 500);
        }
    }
    
    /**
     * 处理GET请求
     */
    private function handleGetRequest($action) {
        switch ($action) {
            case 'config':
                $this->getConfig();
                break;
            case 'keys':
                $this->getApiKeysHandler();
                break;
            case 'stats':
                $this->getStats();
                break;
            case 'models':
                $this->getAvailableModels();
                break;
            default:
                $this->respondError('无效的操作', 400);
        }
    }
    
    /**
     * 处理POST请求
     */
    private function handlePostRequest($action) {
        switch ($action) {
            case 'add_key':
                $this->addApiKeyHandler();
                break;
            case 'validate_key':
                $this->validateApiKey();
                break;
            case 'test_connection':
                $this->testConnection();
                break;
            case 'chat':
                $this->handleChatRequest();
                break;
            default:
                $this->respondError('无效的操作', 400);
        }
    }
    
    /**
     * 处理PUT请求
     */
    private function handlePutRequest($action) {
        switch ($action) {
            case 'config':
                $this->updateConfig();
                break;
            case 'toggle':
                $this->toggleService();
                break;
            default:
                $this->respondError('无效的操作', 400);
        }
    }
    
    /**
     * 处理DELETE请求
     */
    private function handleDeleteRequest($action) {
        switch ($action) {
            case 'key':
                $this->deleteApiKeyHandler();
                break;
            default:
                $this->respondError('无效的操作', 400);
        }
    }
    
    /**
     * 获取配置
     */
    private function getConfig() {
        $config = $this->getServiceConfig();
        if (!$config) {
            $this->respondError('配置不存在', 404);
        }
        
        $this->respondSuccess($config, '获取配置成功');
    }
    
    /**
     * 更新配置
     */
    private function updateConfig() {
        $data = $this->request;
        
        // 验证必要字段
        $requiredFields = ['model_name', 'system_prompt'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $this->respondError("缺少必要字段: {$field}", 400);
            }
        }
        
        // 验证数值字段
        if (isset($data['reply_delay'])) {
            $data['reply_delay'] = max(0, min(60, (int)$data['reply_delay']));
        }
        
        if (isset($data['max_tokens'])) {
            $data['max_tokens'] = max(1, min(32000, (int)$data['max_tokens']));
        }
        
        if (isset($data['temperature'])) {
            $data['temperature'] = max(0.0, min(2.0, (float)$data['temperature']));
        }
        
        $success = $this->updateServiceConfig($data);
        
        if ($success) {
            $this->logServiceAction('update_config', null, $data);
            $this->respondSuccess([], '配置更新成功');
        } else {
            $this->respondError('配置更新失败', 500);
        }
    }
    
    /**
     * 切换服务启用状态
     */
    private function toggleService() {
        $enabled = isset($this->request['enabled']) ? (bool)$this->request['enabled'] : false;
        
        $success = $this->updateServiceConfig(['is_enabled' => $enabled ? 1 : 0]);
        
        if ($success) {
            $this->logServiceAction('toggle_service', null, ['enabled' => $enabled]);
            $this->respondSuccess(['enabled' => $enabled], $enabled ? 'Doubao服务已启用' : 'Doubao服务已禁用');
        } else {
            $this->respondError('服务状态更新失败', 500);
        }
    }
    
    /**
     * 添加API密钥
     */
    private function addApiKeyHandler() {
        $apiKey = $this->request['api_key'] ?? '';
        
        if (empty($apiKey)) {
            $this->respondError('API密钥不能为空', 400);
        }
        
        // 验证Doubao API密钥格式（通常是较长的字符串）
        if (strlen($apiKey) < 20) {
            $this->respondError('Doubao API密钥格式无效', 400);
        }
        
        $result = $this->addApiKey($apiKey);
        
        if ($result['success']) {
            $this->logServiceAction('add_key', $result['key_id']);
            $this->respondSuccess(['key_id' => $result['key_id']], $result['message']);
        } else {
            $this->respondError($result['message'], 400);
        }
    }
    
    /**
     * 删除API密钥
     */
    private function deleteApiKeyHandler() {
        $keyId = $this->request['key_id'] ?? 0;
        
        if (empty($keyId)) {
            $this->respondError('密钥ID不能为空', 400);
        }
        
        $result = $this->deleteApiKey($keyId);
        
        if ($result['success']) {
            $this->logServiceAction('delete_key', $keyId);
            $this->respondSuccess([], $result['message']);
        } else {
            $this->respondError($result['message'], 400);
        }
    }
    
    /**
     * 获取API密钥列表
     */
    private function getApiKeysHandler() {
        $keys = $this->getApiKeys();
        $this->respondSuccess($keys, '获取API密钥列表成功');
    }
    
    /**
     * 验证API密钥
     */
    private function validateApiKey() {
        $keyId = $this->request['key_id'] ?? 0;
        $apiKey = $this->request['api_key'] ?? '';
        
        if (empty($keyId) && empty($apiKey)) {
            $this->respondError('密钥ID或API密钥不能为空', 400);
        }
        
        // 如果提供了keyId，从数据库获取API密钥
        if (!empty($keyId)) {
            $keyData = $this->fetchOne(
                "SELECT id, api_key FROM ai_service_api_keys WHERE id = ? AND service_name = ?",
                [$keyId, $this->serviceName]
            );
            
            if (!$keyData) {
                $this->respondError('API密钥不存在', 404);
            }
            
            $apiKey = $this->decryptApiKey($keyData['api_key']);
        }
        
        $startTime = microtime(true);
        $result = $this->validateDoubaoApiKey($apiKey);
        $responseTime = round((microtime(true) - $startTime) * 1000);
        
        // 如果有keyId，更新数据库中的验证状态
        if (!empty($keyId)) {
            $sql = "UPDATE ai_service_api_keys SET is_valid = ?, last_validated_at = CURRENT_TIMESTAMP, validation_error = ? WHERE id = ?";
            $this->safeQuery($sql, [$result['valid'] ? 1 : 0, $result['valid'] ? null : $result['error'], $keyId]);
        }
        
        $this->logServiceAction('validate_key', $keyId ?? null, ['api_key_masked' => substr($apiKey, 0, 8) . '...'], $result, $result['valid'] ? 'success' : 'error', $result['valid'] ? null : $result['error'], $responseTime);
        
        if ($result['valid']) {
            $this->respondSuccess($result, 'API密钥验证成功');
        } else {
            $this->respondError($result['error'], 400);
        }
    }

    /**
     * 验证Doubao API密钥
     */
    private function validateDoubaoApiKey($apiKey) {
        if (empty($apiKey) || strlen($apiKey) < 20) {
            return [
                'valid' => false,
                'error' => 'API密钥格式不正确'
            ];
        }

        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode([
                    'model' => 'doubao-seed-1-6-250615',
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => 'test'
                        ]
                    ],
                    'max_tokens' => 1
                ]),
                CURLOPT_TIMEOUT => 10,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json'
                ],
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return [
                    'valid' => false,
                    'error' => '网络连接错误: ' . $error
                ];
            }

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if (isset($data['choices']) && is_array($data['choices'])) {
                    return [
                        'valid' => true,
                        'model' => $data['model'] ?? 'doubao-seed-1-6-250615'
                    ];
                }
            } elseif ($httpCode === 401) {
                return [
                    'valid' => false,
                    'error' => 'API密钥无效或已过期'
                ];
            } elseif ($httpCode === 429) {
                return [
                    'valid' => false,
                    'error' => 'API请求频率超限，请稍后重试'
                ];
            }

            return [
                'valid' => false,
                'error' => 'API验证失败，HTTP状态码: ' . $httpCode
            ];

        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => '验证过程中发生异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 测试连接
     */
    private function testConnection() {
        if (!$this->isServiceEnabled()) {
            $this->respondError('Doubao服务未启用', 403);
        }

        $keyData = $this->getValidApiKey();
        if (!$keyData) {
            $this->respondError('没有可用的API密钥', 400);
        }

        $startTime = microtime(true);
        $result = $this->validateDoubaoApiKey($keyData['api_key']);
        $responseTime = round((microtime(true) - $startTime) * 1000);

        $this->logServiceAction('test_connection', $keyData['id'], null, $result, $result['valid'] ? 'success' : 'error', $result['valid'] ? null : $result['error'], $responseTime);

        if ($result['valid']) {
            $this->respondSuccess(['response_time' => $responseTime], 'Doubao连接测试成功');
        } else {
            $this->respondError($result['error'], 400);
        }
    }

    /**
     * 处理聊天请求
     */
    private function handleChatRequest() {
        if (!$this->isServiceEnabled()) {
            $this->respondError('Doubao服务未启用', 403);
        }

        $keyData = $this->getValidApiKey();
        if (!$keyData) {
            $this->respondError('没有可用的API密钥', 400);
        }

        $messages = $this->request['messages'] ?? [];
        if (empty($messages)) {
            $this->respondError('消息不能为空', 400);
        }

        $config = $this->getServiceConfig();

        $requestData = [
            'model' => $config['model_name'],
            'messages' => $messages,
            'max_tokens' => $config['max_tokens'],
            'temperature' => (float)$config['temperature'],
            'stream' => false
        ];

        $startTime = microtime(true);
        $result = $this->callDoubaoApi($keyData['api_key'], $requestData);
        $responseTime = round((microtime(true) - $startTime) * 1000);

        if ($result['success']) {
            $this->updateApiKeyUsage($keyData['id']);
            $this->logServiceAction('chat', $keyData['id'], $requestData, $result['data'], 'success', null, $responseTime);
            $this->respondSuccess($result['data'], '聊天请求成功');
        } else {
            $this->logServiceAction('chat', $keyData['id'], $requestData, null, 'error', $result['error'], $responseTime);
            $this->respondError($result['error'], 500);
        }
    }

    /**
     * 调用Doubao API
     */
    private function callDoubaoApi($apiKey, $requestData) {
        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($requestData),
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json'
                ],
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return [
                    'success' => false,
                    'error' => '网络连接错误: ' . $error
                ];
            }

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return [
                        'success' => true,
                        'data' => $data
                    ];
                } else {
                    return [
                        'success' => false,
                        'error' => 'API响应格式错误'
                    ];
                }
            } else {
                $errorData = json_decode($response, true);
                $errorMessage = $errorData['error']['message'] ?? 'API请求失败';

                return [
                    'success' => false,
                    'error' => $errorMessage . ' (HTTP ' . $httpCode . ')'
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => '请求过程中发生异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取统计信息
     */
    private function getStats() {
        $stats = $this->getServiceStats();
        $this->respondSuccess($stats, '获取统计信息成功');
    }

    /**
     * 获取可用模型
     */
    private function getAvailableModels() {
        $models = [
            [
                'id' => 'doubao-seed-1-6-250615',
                'name' => '豆包 Seed 1.6',
                'description' => '通用对话模型，适合日常客服场景'
            ],
            [
                'id' => 'doubao-1.5-vision-pro-250328',
                'name' => '豆包 1.5 Vision Pro',
                'description' => '视觉增强模型，支持图像理解'
            ],
            [
                'id' => 'doubao-seed-1-6-thinking-250715',
                'name' => '豆包 Seed 1.6 Thinking',
                'description' => '推理模型，支持深度思考功能'
            ]
        ];

        $this->respondSuccess($models, '获取可用模型成功');
    }
}

// 处理请求
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    $service = new DoubaoService();
    $service->handleRequest();
}
