<?php
/**
 * AI服务验证器API接口
 * 提供统一的AI服务验证和测试功能
 * 
 * @version 1.0.0
 */

// 定义API访问常量
define('API_ACCESS', true);

require_once __DIR__ . '/ai_service_base.php';

class AiServiceValidator extends ApiBase {
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            $action = $this->request['action'] ?? '';
            
            switch ($this->method) {
                case 'POST':
                    $this->handlePostRequest($action);
                    break;
                case 'GET':
                    $this->handleGetRequest($action);
                    break;
                default:
                    $this->respondError('不支持的请求方法', 405);
            }
        } catch (Exception $e) {
            error_log("AI服务验证器错误: " . $e->getMessage());
            $this->respondError('服务器内部错误', 500);
        }
    }
    
    /**
     * 处理GET请求
     */
    private function handleGetRequest($action) {
        switch ($action) {
            case 'services':
                $this->getAvailableServices();
                break;
            case 'status':
                $this->getServicesStatus();
                break;
            default:
                $this->respondError('无效的操作', 400);
        }
    }
    
    /**
     * 处理POST请求
     */
    private function handlePostRequest($action) {
        switch ($action) {
            case 'validate_deepseek':
                $this->validateDeepSeekKey();
                break;
            case 'validate_doubao':
                $this->validateDoubaoKey();
                break;
            case 'test_all_services':
                $this->testAllServices();
                break;
            case 'batch_validate':
                $this->batchValidateKeys();
                break;
            default:
                $this->respondError('无效的操作', 400);
        }
    }
    
    /**
     * 验证DeepSeek API密钥
     */
    private function validateDeepSeekKey() {
        $apiKey = $this->request['api_key'] ?? '';
        
        if (empty($apiKey)) {
            $this->respondError('API密钥不能为空', 400);
        }
        
        $startTime = microtime(true);
        $result = $this->validateDeepSeekApiKey($apiKey);
        $responseTime = round((microtime(true) - $startTime) * 1000);
        
        $result['response_time'] = $responseTime;
        $result['service'] = 'deepseek';
        
        if ($result['valid']) {
            $this->respondSuccess($result, 'DeepSeek API密钥验证成功');
        } else {
            $this->respondError($result['error'], 400, 'VALIDATION_FAILED');
        }
    }
    
    /**
     * 验证Doubao API密钥
     */
    private function validateDoubaoKey() {
        $apiKey = $this->request['api_key'] ?? '';
        
        if (empty($apiKey)) {
            $this->respondError('API密钥不能为空', 400);
        }
        
        $startTime = microtime(true);
        $result = $this->validateDoubaoApiKey($apiKey);
        $responseTime = round((microtime(true) - $startTime) * 1000);
        
        $result['response_time'] = $responseTime;
        $result['service'] = 'doubao';
        
        if ($result['valid']) {
            $this->respondSuccess($result, 'Doubao API密钥验证成功');
        } else {
            $this->respondError($result['error'], 400, 'VALIDATION_FAILED');
        }
    }
    
    /**
     * 测试所有服务
     */
    private function testAllServices() {
        $results = [];
        
        // 测试DeepSeek服务
        $deepseekResult = $this->testServiceConnection('deepseek');
        $results['deepseek'] = $deepseekResult;
        
        // 测试Doubao服务
        $doubaoResult = $this->testServiceConnection('doubao');
        $results['doubao'] = $doubaoResult;
        
        // 计算总体状态
        $allValid = $deepseekResult['valid'] && $doubaoResult['valid'];
        $message = $allValid ? '所有服务连接正常' : '部分服务连接异常';
        
        $this->respondSuccess($results, $message);
    }
    
    /**
     * 测试服务连接
     */
    private function testServiceConnection($serviceName) {
        try {
            // 获取服务配置
            $config = $this->fetchOne(
                "SELECT * FROM ai_service_configs WHERE service_name = ?",
                [$serviceName]
            );
            
            if (!$config) {
                return [
                    'valid' => false,
                    'error' => '服务配置不存在',
                    'service' => $serviceName
                ];
            }
            
            if (!$config['is_enabled']) {
                return [
                    'valid' => false,
                    'error' => '服务未启用',
                    'service' => $serviceName,
                    'enabled' => false
                ];
            }
            
            // 获取有效的API密钥
            $keyData = $this->fetchOne(
                "SELECT id, api_key FROM ai_service_api_keys WHERE service_name = ? AND is_active = 1 AND is_valid = 1 ORDER BY last_used_at ASC LIMIT 1",
                [$serviceName]
            );
            
            if (!$keyData) {
                return [
                    'valid' => false,
                    'error' => '没有可用的API密钥',
                    'service' => $serviceName,
                    'enabled' => true
                ];
            }
            
            // 解密API密钥
            $encryptionKey = $this->config->getApiSecretKey();
            $encryptedKey = $keyData['api_key'];
            $data = base64_decode($encryptedKey);
            $iv = substr($data, 0, 16);
            $encrypted = substr($data, 16);
            $apiKey = openssl_decrypt($encrypted, 'AES-256-CBC', $encryptionKey, 0, $iv);
            
            // 验证API密钥
            $startTime = microtime(true);
            if ($serviceName === 'deepseek') {
                $result = $this->validateDeepSeekApiKey($apiKey);
            } else {
                $result = $this->validateDoubaoApiKey($apiKey);
            }
            $responseTime = round((microtime(true) - $startTime) * 1000);
            
            $result['service'] = $serviceName;
            $result['response_time'] = $responseTime;
            $result['enabled'] = true;
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => '测试过程中发生异常: ' . $e->getMessage(),
                'service' => $serviceName
            ];
        }
    }
    
    /**
     * 批量验证API密钥
     */
    private function batchValidateKeys() {
        $keys = $this->request['keys'] ?? [];
        
        if (empty($keys) || !is_array($keys)) {
            $this->respondError('密钥列表不能为空', 400);
        }
        
        $results = [];
        
        foreach ($keys as $keyData) {
            $service = $keyData['service'] ?? '';
            $apiKey = $keyData['api_key'] ?? '';
            
            if (empty($service) || empty($apiKey)) {
                $results[] = [
                    'valid' => false,
                    'error' => '服务名称或API密钥为空',
                    'service' => $service
                ];
                continue;
            }
            
            $startTime = microtime(true);
            
            if ($service === 'deepseek') {
                $result = $this->validateDeepSeekApiKey($apiKey);
            } elseif ($service === 'doubao') {
                $result = $this->validateDoubaoApiKey($apiKey);
            } else {
                $result = [
                    'valid' => false,
                    'error' => '不支持的服务类型'
                ];
            }
            
            $responseTime = round((microtime(true) - $startTime) * 1000);
            $result['service'] = $service;
            $result['response_time'] = $responseTime;
            $result['api_key_masked'] = substr($apiKey, 0, 8) . '...';
            
            $results[] = $result;
        }
        
        $this->respondSuccess($results, '批量验证完成');
    }
    
    /**
     * 获取可用服务列表
     */
    private function getAvailableServices() {
        $services = [
            [
                'name' => 'deepseek',
                'display_name' => 'DeepSeek',
                'description' => 'DeepSeek AI智能对话服务',
                'api_endpoint' => 'https://api.deepseek.com',
                'models' => [
                    'deepseek-chat',
                    'deepseek-reasoner'
                ]
            ],
            [
                'name' => 'doubao',
                'display_name' => '豆包 (Doubao)',
                'description' => '字节跳动豆包AI智能对话服务',
                'api_endpoint' => 'https://ark.cn-beijing.volces.com',
                'models' => [
                    'doubao-seed-1-6-250615',
                    'doubao-1.5-vision-pro-250328',
                    'doubao-seed-1-6-thinking-250715'
                ]
            ]
        ];
        
        $this->respondSuccess($services, '获取可用服务列表成功');
    }
    
    /**
     * 获取服务状态
     */
    private function getServicesStatus() {
        $services = $this->fetchAll("SELECT service_name, is_enabled FROM ai_service_configs");
        
        $status = [];
        foreach ($services as $service) {
            $serviceName = $service['service_name'];
            
            // 获取API密钥统计
            $keyStats = $this->fetchOne(
                "SELECT COUNT(*) as total, SUM(is_valid) as valid FROM ai_service_api_keys WHERE service_name = ? AND is_active = 1",
                [$serviceName]
            );
            
            $status[$serviceName] = [
                'enabled' => (bool)$service['is_enabled'],
                'total_keys' => (int)$keyStats['total'],
                'valid_keys' => (int)$keyStats['valid']
            ];
        }
        
        $this->respondSuccess($status, '获取服务状态成功');
    }

    /**
     * 验证DeepSeek API密钥
     */
    private function validateDeepSeekApiKey($apiKey) {
        if (empty($apiKey) || !preg_match('/^sk-[a-zA-Z0-9]{48}$/', $apiKey)) {
            return [
                'valid' => false,
                'error' => 'API密钥格式不正确，应为sk-开头的50位字符'
            ];
        }

        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'https://api.deepseek.com/v1/models',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json'
                ],
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return [
                    'valid' => false,
                    'error' => '网络连接错误: ' . $error
                ];
            }

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if (isset($data['data']) && is_array($data['data'])) {
                    return [
                        'valid' => true,
                        'models' => array_column($data['data'], 'id')
                    ];
                }
            } elseif ($httpCode === 401) {
                return [
                    'valid' => false,
                    'error' => 'API密钥无效或已过期'
                ];
            } elseif ($httpCode === 429) {
                return [
                    'valid' => false,
                    'error' => 'API请求频率超限，请稍后重试'
                ];
            }

            return [
                'valid' => false,
                'error' => 'API验证失败，HTTP状态码: ' . $httpCode
            ];

        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => '验证过程中发生异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证Doubao API密钥
     */
    private function validateDoubaoApiKey($apiKey) {
        if (empty($apiKey) || strlen($apiKey) < 20) {
            return [
                'valid' => false,
                'error' => 'API密钥格式不正确'
            ];
        }

        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode([
                    'model' => 'doubao-seed-1-6-250615',
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => 'test'
                        ]
                    ],
                    'max_tokens' => 1
                ]),
                CURLOPT_TIMEOUT => 10,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json'
                ],
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return [
                    'valid' => false,
                    'error' => '网络连接错误: ' . $error
                ];
            }

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if (isset($data['choices']) && is_array($data['choices'])) {
                    return [
                        'valid' => true,
                        'model' => $data['model'] ?? 'doubao-seed-1-6-250615'
                    ];
                }
            } elseif ($httpCode === 401) {
                return [
                    'valid' => false,
                    'error' => 'API密钥无效或已过期'
                ];
            } elseif ($httpCode === 429) {
                return [
                    'valid' => false,
                    'error' => 'API请求频率超限，请稍后重试'
                ];
            }

            return [
                'valid' => false,
                'error' => 'API验证失败，HTTP状态码: ' . $httpCode
            ];

        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => '验证过程中发生异常: ' . $e->getMessage()
            ];
        }
    }
}

// 处理请求
if ($_SERVER['REQUEST_METHOD'] !== 'OPTIONS') {
    $validator = new AiServiceValidator();
    $validator->handleRequest();
}
