<?php
/**
 * AI服务错误处理器
 * 提供统一的错误处理、验证和异常管理功能
 * 
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => 'Access denied']);
    exit;
}

class AiServiceErrorHandler {
    private $logger;
    private $config;
    
    // 错误代码常量
    const ERROR_INVALID_REQUEST = 'INVALID_REQUEST';
    const ERROR_MISSING_PARAMETER = 'MISSING_PARAMETER';
    const ERROR_INVALID_PARAMETER = 'INVALID_PARAMETER';
    const ERROR_SERVICE_DISABLED = 'SERVICE_DISABLED';
    const ERROR_NO_VALID_API_KEY = 'NO_VALID_API_KEY';
    const ERROR_API_KEY_INVALID = 'API_KEY_INVALID';
    const ERROR_API_RATE_LIMIT = 'API_RATE_LIMIT';
    const ERROR_API_NETWORK = 'API_NETWORK_ERROR';
    const ERROR_API_TIMEOUT = 'API_TIMEOUT';
    const ERROR_DATABASE = 'DATABASE_ERROR';
    const ERROR_INTERNAL = 'INTERNAL_ERROR';
    const ERROR_VALIDATION_FAILED = 'VALIDATION_FAILED';
    const ERROR_UNAUTHORIZED = 'UNAUTHORIZED';
    const ERROR_FORBIDDEN = 'FORBIDDEN';
    
    /**
     * 构造函数
     */
    public function __construct($logger, $config) {
        $this->logger = $logger;
        $this->config = $config;
    }
    
    /**
     * 处理异常
     */
    public function handleException($exception, $serviceName = null, $context = []) {
        $errorCode = $this->getErrorCodeFromException($exception);
        $errorMessage = $exception->getMessage();
        
        // 记录错误日志
        $this->logger->logError($errorMessage, array_merge($context, [
            'exception_type' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]));
        
        // 返回用户友好的错误信息
        return $this->formatError($errorCode, $this->getUserFriendlyMessage($errorCode, $errorMessage));
    }
    
    /**
     * 验证请求参数
     */
    public function validateRequest($request, $requiredFields = [], $optionalFields = []) {
        $errors = [];
        
        // 检查必需字段
        foreach ($requiredFields as $field => $rules) {
            if (!isset($request[$field])) {
                $errors[] = $this->formatError(self::ERROR_MISSING_PARAMETER, "缺少必需参数: {$field}");
                continue;
            }
            
            $value = $request[$field];
            $validationResult = $this->validateField($field, $value, $rules);
            
            if (!$validationResult['valid']) {
                $errors[] = $this->formatError(self::ERROR_INVALID_PARAMETER, $validationResult['message']);
            }
        }
        
        // 检查可选字段
        foreach ($optionalFields as $field => $rules) {
            if (isset($request[$field])) {
                $value = $request[$field];
                $validationResult = $this->validateField($field, $value, $rules);
                
                if (!$validationResult['valid']) {
                    $errors[] = $this->formatError(self::ERROR_INVALID_PARAMETER, $validationResult['message']);
                }
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 验证单个字段
     */
    private function validateField($fieldName, $value, $rules) {
        foreach ($rules as $rule => $ruleValue) {
            switch ($rule) {
                case 'type':
                    if (!$this->validateType($value, $ruleValue)) {
                        return [
                            'valid' => false,
                            'message' => "参数 {$fieldName} 类型错误，期望: {$ruleValue}"
                        ];
                    }
                    break;
                    
                case 'min_length':
                    if (is_string($value) && strlen($value) < $ruleValue) {
                        return [
                            'valid' => false,
                            'message' => "参数 {$fieldName} 长度不能少于 {$ruleValue} 个字符"
                        ];
                    }
                    break;
                    
                case 'max_length':
                    if (is_string($value) && strlen($value) > $ruleValue) {
                        return [
                            'valid' => false,
                            'message' => "参数 {$fieldName} 长度不能超过 {$ruleValue} 个字符"
                        ];
                    }
                    break;
                    
                case 'min_value':
                    if (is_numeric($value) && $value < $ruleValue) {
                        return [
                            'valid' => false,
                            'message' => "参数 {$fieldName} 值不能小于 {$ruleValue}"
                        ];
                    }
                    break;
                    
                case 'max_value':
                    if (is_numeric($value) && $value > $ruleValue) {
                        return [
                            'valid' => false,
                            'message' => "参数 {$fieldName} 值不能大于 {$ruleValue}"
                        ];
                    }
                    break;
                    
                case 'pattern':
                    if (is_string($value) && !preg_match($ruleValue, $value)) {
                        return [
                            'valid' => false,
                            'message' => "参数 {$fieldName} 格式不正确"
                        ];
                    }
                    break;
                    
                case 'in':
                    if (!in_array($value, $ruleValue)) {
                        return [
                            'valid' => false,
                            'message' => "参数 {$fieldName} 值无效，允许的值: " . implode(', ', $ruleValue)
                        ];
                    }
                    break;
            }
        }
        
        return ['valid' => true];
    }
    
    /**
     * 验证数据类型
     */
    private function validateType($value, $expectedType) {
        switch ($expectedType) {
            case 'string':
                return is_string($value);
            case 'integer':
                return is_int($value) || (is_string($value) && ctype_digit($value));
            case 'float':
                return is_float($value) || is_numeric($value);
            case 'boolean':
                return is_bool($value) || in_array($value, [0, 1, '0', '1', 'true', 'false']);
            case 'array':
                return is_array($value);
            default:
                return true;
        }
    }
    
    /**
     * 验证API密钥格式
     */
    public function validateApiKeyFormat($apiKey, $serviceName) {
        switch ($serviceName) {
            case 'deepseek':
                if (!preg_match('/^sk-[a-zA-Z0-9]{48}$/', $apiKey)) {
                    return $this->formatError(self::ERROR_INVALID_PARAMETER, 'DeepSeek API密钥格式无效，应为sk-开头的50位字符');
                }
                break;
                
            case 'doubao':
                if (strlen($apiKey) < 20) {
                    return $this->formatError(self::ERROR_INVALID_PARAMETER, 'Doubao API密钥格式无效');
                }
                break;
                
            default:
                return $this->formatError(self::ERROR_INVALID_PARAMETER, '不支持的服务类型');
        }
        
        return ['valid' => true];
    }
    
    /**
     * 处理API错误响应
     */
    public function handleApiError($httpCode, $response, $serviceName) {
        $errorData = json_decode($response, true);
        $errorMessage = $errorData['error']['message'] ?? '未知API错误';
        
        switch ($httpCode) {
            case 400:
                return $this->formatError(self::ERROR_INVALID_REQUEST, "API请求参数错误: {$errorMessage}");
                
            case 401:
                return $this->formatError(self::ERROR_API_KEY_INVALID, "API密钥无效或已过期");
                
            case 403:
                return $this->formatError(self::ERROR_FORBIDDEN, "API访问被拒绝: {$errorMessage}");
                
            case 429:
                return $this->formatError(self::ERROR_API_RATE_LIMIT, "API请求频率超限，请稍后重试");
                
            case 500:
            case 502:
            case 503:
            case 504:
                return $this->formatError(self::ERROR_INTERNAL, "{$serviceName} 服务暂时不可用，请稍后重试");
                
            default:
                return $this->formatError(self::ERROR_INTERNAL, "API请求失败 (HTTP {$httpCode}): {$errorMessage}");
        }
    }
    
    /**
     * 处理网络错误
     */
    public function handleNetworkError($curlError) {
        if (strpos($curlError, 'timeout') !== false) {
            return $this->formatError(self::ERROR_API_TIMEOUT, '请求超时，请检查网络连接');
        }
        
        return $this->formatError(self::ERROR_API_NETWORK, "网络连接错误: {$curlError}");
    }
    
    /**
     * 格式化错误响应
     */
    public function formatError($errorCode, $message, $details = null) {
        $error = [
            'error_code' => $errorCode,
            'message' => $message,
            'timestamp' => date('c')
        ];
        
        if ($details) {
            $error['details'] = $details;
        }
        
        if ($this->config->isDebugMode()) {
            $error['debug'] = [
                'request_id' => uniqid(),
                'server_time' => time()
            ];
        }
        
        return $error;
    }
    
    /**
     * 从异常获取错误代码
     */
    private function getErrorCodeFromException($exception) {
        $message = strtolower($exception->getMessage());
        
        if (strpos($message, 'database') !== false || strpos($message, 'sql') !== false) {
            return self::ERROR_DATABASE;
        }
        
        if (strpos($message, 'network') !== false || strpos($message, 'connection') !== false) {
            return self::ERROR_API_NETWORK;
        }
        
        if (strpos($message, 'timeout') !== false) {
            return self::ERROR_API_TIMEOUT;
        }
        
        if (strpos($message, 'validation') !== false) {
            return self::ERROR_VALIDATION_FAILED;
        }
        
        return self::ERROR_INTERNAL;
    }
    
    /**
     * 获取用户友好的错误信息
     */
    private function getUserFriendlyMessage($errorCode, $originalMessage) {
        $friendlyMessages = [
            self::ERROR_DATABASE => '数据库操作失败，请稍后重试',
            self::ERROR_API_NETWORK => '网络连接异常，请检查网络设置',
            self::ERROR_API_TIMEOUT => '请求超时，请稍后重试',
            self::ERROR_VALIDATION_FAILED => '数据验证失败',
            self::ERROR_INTERNAL => '服务器内部错误，请联系管理员'
        ];
        
        return $friendlyMessages[$errorCode] ?? $originalMessage;
    }
    
    /**
     * 检查服务状态
     */
    public function checkServiceHealth($serviceName) {
        $errors = [];
        
        // 检查服务是否启用
        // 这里应该调用相应的服务检查方法
        
        return [
            'healthy' => empty($errors),
            'errors' => $errors
        ];
    }
}
