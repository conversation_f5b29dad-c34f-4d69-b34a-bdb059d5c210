<?php
/**
 * AI服务日志记录器
 * 提供统一的日志记录、错误处理和监控功能
 * 
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => 'Access denied']);
    exit;
}

class AiServiceLogger {
    private $db;
    private $logFile;
    private $errorFile;
    private $config;
    
    /**
     * 构造函数
     */
    public function __construct($db, $config) {
        $this->db = $db;
        $this->config = $config;
        
        // 设置日志文件路径
        $logDir = dirname(__DIR__) . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->logFile = $logDir . '/ai_service_' . date('Y-m-d') . '.log';
        $this->errorFile = $logDir . '/ai_service_error_' . date('Y-m-d') . '.log';
    }
    
    /**
     * 记录API请求日志
     */
    public function logApiRequest($serviceName, $action, $requestData = null, $responseData = null, $status = 'success', $errorMessage = null, $responseTime = null, $apiKeyId = null) {
        try {
            // 记录到数据库
            $this->logToDatabase($serviceName, $action, $requestData, $responseData, $status, $errorMessage, $responseTime, $apiKeyId);
            
            // 记录到文件
            $this->logToFile($serviceName, $action, $requestData, $responseData, $status, $errorMessage, $responseTime);
            
        } catch (Exception $e) {
            // 日志记录失败，记录到错误文件
            $this->logError('日志记录失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 记录到数据库
     */
    private function logToDatabase($serviceName, $action, $requestData, $responseData, $status, $errorMessage, $responseTime, $apiKeyId) {
        $sql = "INSERT INTO ai_service_logs (service_name, api_key_id, action, request_data, response_data, status, error_message, response_time, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $serviceName,
            $apiKeyId,
            $action,
            $requestData ? json_encode($requestData, JSON_UNESCAPED_UNICODE) : null,
            $responseData ? json_encode($responseData, JSON_UNESCAPED_UNICODE) : null,
            $status,
            $errorMessage,
            $responseTime,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
    }
    
    /**
     * 记录到文件
     */
    private function logToFile($serviceName, $action, $requestData, $responseData, $status, $errorMessage, $responseTime) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'service' => $serviceName,
            'action' => $action,
            'status' => $status,
            'response_time' => $responseTime,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        if ($errorMessage) {
            $logEntry['error'] = $errorMessage;
        }
        
        if ($requestData && $this->config->isDebugMode()) {
            $logEntry['request'] = $this->sanitizeLogData($requestData);
        }
        
        if ($responseData && $this->config->isDebugMode()) {
            $logEntry['response'] = $this->sanitizeLogData($responseData);
        }
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        file_put_contents($this->logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 记录错误
     */
    public function logError($message, $context = []) {
        $errorEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'ERROR',
            'message' => $message,
            'context' => $context,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        $errorLine = json_encode($errorEntry, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        file_put_contents($this->errorFile, $errorLine, FILE_APPEND | LOCK_EX);
        
        // 同时记录到PHP错误日志
        error_log("AI Service Error: " . $message . " Context: " . json_encode($context));
    }
    
    /**
     * 记录警告
     */
    public function logWarning($message, $context = []) {
        $warningEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'WARNING',
            'message' => $message,
            'context' => $context,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];
        
        $warningLine = json_encode($warningEntry, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        file_put_contents($this->logFile, $warningLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 记录信息
     */
    public function logInfo($message, $context = []) {
        if (!$this->config->isLogApiRequests()) {
            return;
        }
        
        $infoEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'INFO',
            'message' => $message,
            'context' => $context
        ];
        
        $infoLine = json_encode($infoEntry, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        file_put_contents($this->logFile, $infoLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 清理日志数据（移除敏感信息）
     */
    private function sanitizeLogData($data) {
        if (is_array($data)) {
            $sanitized = [];
            foreach ($data as $key => $value) {
                if (in_array(strtolower($key), ['api_key', 'password', 'token', 'secret'])) {
                    $sanitized[$key] = '***REDACTED***';
                } elseif (is_array($value)) {
                    $sanitized[$key] = $this->sanitizeLogData($value);
                } else {
                    $sanitized[$key] = $value;
                }
            }
            return $sanitized;
        }
        
        return $data;
    }
    
    /**
     * 获取日志统计
     */
    public function getLogStats($serviceName = null, $startDate = null, $endDate = null) {
        $whereConditions = [];
        $params = [];
        
        if ($serviceName) {
            $whereConditions[] = "service_name = ?";
            $params[] = $serviceName;
        }
        
        if ($startDate) {
            $whereConditions[] = "created_at >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $whereConditions[] = "created_at <= ?";
            $params[] = $endDate;
        }
        
        $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);
        
        // 总请求数
        $totalSql = "SELECT COUNT(*) as total FROM ai_service_logs $whereClause";
        $stmt = $this->db->prepare($totalSql);
        $stmt->execute($params);
        $total = $stmt->fetchColumn();
        
        // 成功请求数
        $successSql = "SELECT COUNT(*) as success FROM ai_service_logs $whereClause AND status = 'success'";
        $stmt = $this->db->prepare($successSql);
        $stmt->execute($params);
        $success = $stmt->fetchColumn();
        
        // 错误请求数
        $errorSql = "SELECT COUNT(*) as error FROM ai_service_logs $whereClause AND status = 'error'";
        $stmt = $this->db->prepare($errorSql);
        $stmt->execute($params);
        $error = $stmt->fetchColumn();
        
        // 平均响应时间
        $avgTimeSql = "SELECT AVG(response_time) as avg_time FROM ai_service_logs $whereClause AND response_time IS NOT NULL";
        $stmt = $this->db->prepare($avgTimeSql);
        $stmt->execute($params);
        $avgTime = $stmt->fetchColumn();
        
        return [
            'total_requests' => (int)$total,
            'successful_requests' => (int)$success,
            'error_requests' => (int)$error,
            'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0,
            'average_response_time' => $avgTime ? round($avgTime, 2) : null
        ];
    }
    
    /**
     * 清理旧日志
     */
    public function cleanupOldLogs($daysToKeep = 30) {
        try {
            // 清理数据库日志
            $sql = "DELETE FROM ai_service_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$daysToKeep]);
            $deletedRows = $stmt->rowCount();
            
            // 清理文件日志
            $logDir = dirname(__DIR__) . '/logs';
            $cutoffDate = date('Y-m-d', strtotime("-{$daysToKeep} days"));
            
            $deletedFiles = 0;
            if (is_dir($logDir)) {
                $files = glob($logDir . '/ai_service_*.log');
                foreach ($files as $file) {
                    $filename = basename($file);
                    if (preg_match('/ai_service_(\d{4}-\d{2}-\d{2})\.log/', $filename, $matches)) {
                        $fileDate = $matches[1];
                        if ($fileDate < $cutoffDate) {
                            unlink($file);
                            $deletedFiles++;
                        }
                    }
                }
            }
            
            $this->logInfo('日志清理完成', [
                'deleted_db_rows' => $deletedRows,
                'deleted_files' => $deletedFiles,
                'days_kept' => $daysToKeep
            ]);
            
            return [
                'deleted_db_rows' => $deletedRows,
                'deleted_files' => $deletedFiles
            ];
            
        } catch (Exception $e) {
            $this->logError('日志清理失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取最近的错误日志
     */
    public function getRecentErrors($limit = 50, $serviceName = null) {
        $whereConditions = ["status = 'error'"];
        $params = [];
        
        if ($serviceName) {
            $whereConditions[] = "service_name = ?";
            $params[] = $serviceName;
        }
        
        $params[] = $limit;
        
        $sql = "SELECT * FROM ai_service_logs WHERE " . implode(' AND ', $whereConditions) . " ORDER BY created_at DESC LIMIT ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
}
