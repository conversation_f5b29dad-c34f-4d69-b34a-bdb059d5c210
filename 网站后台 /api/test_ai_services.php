<?php
/**
 * AI服务API测试脚本
 * 用于测试和验证所有AI服务API端点
 * 
 * @version 1.0.0
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义API访问常量
define('API_ACCESS', true);

require_once __DIR__ . '/config.php';

class AiServiceTester {
    private $baseUrl;
    private $testResults = [];
    
    public function __construct($baseUrl = null) {
        $this->baseUrl = $baseUrl ?: 'http://localhost' . dirname($_SERVER['REQUEST_URI']);
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests() {
        echo "<h1>AI服务API测试报告</h1>\n";
        echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";
        
        // 测试数据库连接
        $this->testDatabaseConnection();
        
        // 测试DeepSeek服务
        $this->testDeepSeekService();
        
        // 测试Doubao服务
        $this->testDoubaoService();
        
        // 测试验证器
        $this->testValidator();
        
        // 显示测试结果摘要
        $this->displaySummary();
    }
    
    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection() {
        echo "<h2>数据库连接测试</h2>\n";
        
        try {
            $config = new ApiConfig();
            $db = $config->getDatabaseConnection();
            
            // 测试AI服务配置表
            $stmt = $db->query("SELECT COUNT(*) FROM ai_service_configs");
            $configCount = $stmt->fetchColumn();
            
            $this->addResult('database_connection', true, "数据库连接成功，AI服务配置表有 {$configCount} 条记录");
            
            // 测试API密钥表
            $stmt = $db->query("SELECT COUNT(*) FROM ai_service_api_keys");
            $keyCount = $stmt->fetchColumn();
            
            $this->addResult('api_keys_table', true, "API密钥表有 {$keyCount} 条记录");
            
        } catch (Exception $e) {
            $this->addResult('database_connection', false, "数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 测试DeepSeek服务
     */
    private function testDeepSeekService() {
        echo "<h2>DeepSeek服务测试</h2>\n";
        
        $baseUrl = $this->baseUrl . '/deepseek_service.php';
        
        // 测试获取配置
        $this->testApiEndpoint('deepseek_get_config', 'GET', $baseUrl . '?action=config');
        
        // 测试获取可用模型
        $this->testApiEndpoint('deepseek_get_models', 'GET', $baseUrl . '?action=models');
        
        // 测试获取API密钥列表
        $this->testApiEndpoint('deepseek_get_keys', 'GET', $baseUrl . '?action=keys');
        
        // 测试获取统计信息
        $this->testApiEndpoint('deepseek_get_stats', 'GET', $baseUrl . '?action=stats');
        
        // 测试添加无效API密钥
        $this->testApiEndpoint('deepseek_add_invalid_key', 'POST', $baseUrl, [
            'action' => 'add_key',
            'api_key' => 'invalid_key'
        ], false);
        
        // 测试验证无效API密钥
        $this->testApiEndpoint('deepseek_validate_invalid_key', 'POST', $baseUrl, [
            'action' => 'validate_key',
            'api_key' => 'sk-invalid_key_format_test_1234567890123456789012'
        ], false);
        
        // 测试更新配置
        $this->testApiEndpoint('deepseek_update_config', 'PUT', $baseUrl, [
            'action' => 'config',
            'model_name' => 'deepseek-chat',
            'system_prompt' => '测试系统提示词',
            'reply_delay' => 5,
            'max_tokens' => 2000,
            'temperature' => 0.8
        ]);
        
        // 测试切换服务状态
        $this->testApiEndpoint('deepseek_toggle_service', 'PUT', $baseUrl, [
            'action' => 'toggle',
            'enabled' => true
        ]);
    }
    
    /**
     * 测试Doubao服务
     */
    private function testDoubaoService() {
        echo "<h2>Doubao服务测试</h2>\n";
        
        $baseUrl = $this->baseUrl . '/doubao_service.php';
        
        // 测试获取配置
        $this->testApiEndpoint('doubao_get_config', 'GET', $baseUrl . '?action=config');
        
        // 测试获取可用模型
        $this->testApiEndpoint('doubao_get_models', 'GET', $baseUrl . '?action=models');
        
        // 测试获取API密钥列表
        $this->testApiEndpoint('doubao_get_keys', 'GET', $baseUrl . '?action=keys');
        
        // 测试获取统计信息
        $this->testApiEndpoint('doubao_get_stats', 'GET', $baseUrl . '?action=stats');
        
        // 测试添加无效API密钥
        $this->testApiEndpoint('doubao_add_invalid_key', 'POST', $baseUrl, [
            'action' => 'add_key',
            'api_key' => 'short'
        ], false);
        
        // 测试验证无效API密钥
        $this->testApiEndpoint('doubao_validate_invalid_key', 'POST', $baseUrl, [
            'action' => 'validate_key',
            'api_key' => 'invalid_doubao_key_test'
        ], false);
        
        // 测试更新配置
        $this->testApiEndpoint('doubao_update_config', 'PUT', $baseUrl, [
            'action' => 'config',
            'model_name' => 'doubao-seed-1-6-250615',
            'system_prompt' => '测试系统提示词',
            'reply_delay' => 3,
            'max_tokens' => 3000,
            'temperature' => 0.7
        ]);
        
        // 测试切换服务状态
        $this->testApiEndpoint('doubao_toggle_service', 'PUT', $baseUrl, [
            'action' => 'toggle',
            'enabled' => true
        ]);
    }
    
    /**
     * 测试验证器
     */
    private function testValidator() {
        echo "<h2>AI服务验证器测试</h2>\n";
        
        $baseUrl = $this->baseUrl . '/ai_service_validator.php';
        
        // 测试获取可用服务
        $this->testApiEndpoint('validator_get_services', 'GET', $baseUrl . '?action=services');
        
        // 测试获取服务状态
        $this->testApiEndpoint('validator_get_status', 'GET', $baseUrl . '?action=status');
        
        // 测试验证DeepSeek密钥
        $this->testApiEndpoint('validator_deepseek_key', 'POST', $baseUrl, [
            'action' => 'validate_deepseek',
            'api_key' => 'sk-invalid_key_format_test_1234567890123456789012'
        ], false);
        
        // 测试验证Doubao密钥
        $this->testApiEndpoint('validator_doubao_key', 'POST', $baseUrl, [
            'action' => 'validate_doubao',
            'api_key' => 'invalid_doubao_key_test'
        ], false);
        
        // 测试批量验证
        $this->testApiEndpoint('validator_batch_validate', 'POST', $baseUrl, [
            'action' => 'batch_validate',
            'keys' => [
                [
                    'service' => 'deepseek',
                    'api_key' => 'sk-invalid_key_format_test_1234567890123456789012'
                ],
                [
                    'service' => 'doubao',
                    'api_key' => 'invalid_doubao_key_test'
                ]
            ]
        ], false);
        
        // 测试所有服务
        $this->testApiEndpoint('validator_test_all', 'POST', $baseUrl, [
            'action' => 'test_all_services'
        ]);
    }
    
    /**
     * 测试API端点
     */
    private function testApiEndpoint($testName, $method, $url, $data = null, $expectSuccess = true) {
        try {
            $ch = curl_init();
            
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);
            
            if ($method === 'POST') {
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
            } elseif ($method === 'PUT') {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
            } elseif ($method === 'DELETE') {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
            }
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                $this->addResult($testName, false, "网络错误: {$error}");
                return;
            }
            
            $responseData = json_decode($response, true);
            
            if ($expectSuccess) {
                if ($httpCode >= 200 && $httpCode < 300 && isset($responseData['success']) && $responseData['success']) {
                    $this->addResult($testName, true, "请求成功: " . ($responseData['message'] ?? ''));
                } else {
                    $this->addResult($testName, false, "请求失败 (HTTP {$httpCode}): " . ($responseData['message'] ?? $response));
                }
            } else {
                // 期望失败的测试
                if ($httpCode >= 400 || (isset($responseData['success']) && !$responseData['success'])) {
                    $this->addResult($testName, true, "按预期失败: " . ($responseData['message'] ?? ''));
                } else {
                    $this->addResult($testName, false, "应该失败但成功了");
                }
            }
            
        } catch (Exception $e) {
            $this->addResult($testName, false, "异常: " . $e->getMessage());
        }
    }
    
    /**
     * 添加测试结果
     */
    private function addResult($testName, $success, $message) {
        $this->testResults[] = [
            'test' => $testName,
            'success' => $success,
            'message' => $message
        ];
        
        $status = $success ? '✅ 通过' : '❌ 失败';
        echo "<p><strong>{$testName}:</strong> {$status} - {$message}</p>\n";
    }
    
    /**
     * 显示测试摘要
     */
    private function displaySummary() {
        echo "<h2>测试摘要</h2>\n";
        
        $total = count($this->testResults);
        $passed = count(array_filter($this->testResults, function($result) {
            return $result['success'];
        }));
        $failed = $total - $passed;
        
        echo "<p><strong>总测试数:</strong> {$total}</p>\n";
        echo "<p><strong>通过:</strong> {$passed}</p>\n";
        echo "<p><strong>失败:</strong> {$failed}</p>\n";
        echo "<p><strong>成功率:</strong> " . round(($passed / $total) * 100, 2) . "%</p>\n";
        
        if ($failed > 0) {
            echo "<h3>失败的测试:</h3>\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "<p>❌ <strong>{$result['test']}:</strong> {$result['message']}</p>\n";
                }
            }
        }
    }
}

// 运行测试
if (isset($_GET['run_tests'])) {
    $tester = new AiServiceTester();
    $tester->runAllTests();
} else {
    echo "<h1>AI服务API测试</h1>\n";
    echo "<p><a href='?run_tests=1'>点击运行测试</a></p>\n";
}
?>
