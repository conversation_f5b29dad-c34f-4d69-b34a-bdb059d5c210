# AI服务API接口文档

## 概述

本文档描述了AI客服配置系统的后端API接口，包括DeepSeek和Doubao两个独立的AI服务。每个服务都提供完整的CRUD操作、API密钥管理、启用/禁用功能和错误处理。

## 系统架构

### 数据库表结构

1. **ai_service_configs** - AI服务配置主表
2. **ai_service_api_keys** - AI服务API密钥表
3. **ai_service_logs** - AI服务使用日志表

### API端点

1. **deepseek_service.php** - DeepSeek服务独立API
2. **doubao_service.php** - Doubao服务独立API
3. **ai_service_validator.php** - 统一验证服务
4. **setup_ai_services.php** - 数据库设置脚本
5. **test_ai_services.php** - API测试脚本

## DeepSeek API接口

### 基础URL
```
/api/deepseek_service.php
```

### 1. 获取配置
```http
GET /api/deepseek_service.php?action=config
```

**响应示例:**
```json
{
  "success": true,
  "message": "获取配置成功",
  "data": {
    "id": 1,
    "service_name": "deepseek",
    "is_enabled": 1,
    "model_name": "deepseek-chat",
    "system_prompt": "你是一名专业的微信小店客服人员...",
    "reply_delay": 0,
    "thinking_enabled": 0,
    "max_tokens": 4000,
    "temperature": 0.70
  }
}
```

### 2. 更新配置
```http
PUT /api/deepseek_service.php
Content-Type: application/json

{
  "action": "config",
  "model_name": "deepseek-chat",
  "system_prompt": "自定义系统提示词",
  "reply_delay": 5,
  "thinking_enabled": 1,
  "max_tokens": 2000,
  "temperature": 0.8
}
```

### 3. 启用/禁用服务
```http
PUT /api/deepseek_service.php
Content-Type: application/json

{
  "action": "toggle",
  "enabled": true
}
```

### 4. 添加API密钥
```http
POST /api/deepseek_service.php
Content-Type: application/json

{
  "action": "add_key",
  "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

### 5. 验证API密钥
```http
POST /api/deepseek_service.php
Content-Type: application/json

{
  "action": "validate_key",
  "key_id": 1
}
```

或者直接验证密钥：
```http
POST /api/deepseek_service.php
Content-Type: application/json

{
  "action": "validate_key",
  "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

### 6. 删除API密钥
```http
DELETE /api/deepseek_service.php
Content-Type: application/json

{
  "action": "key",
  "key_id": 1
}
```

### 7. 获取API密钥列表
```http
GET /api/deepseek_service.php?action=keys
```

### 8. 测试连接
```http
POST /api/deepseek_service.php
Content-Type: application/json

{
  "action": "test_connection"
}
```

### 9. 聊天请求
```http
POST /api/deepseek_service.php
Content-Type: application/json

{
  "action": "chat",
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ]
}
```

### 10. 获取可用模型
```http
GET /api/deepseek_service.php?action=models
```

### 11. 获取统计信息
```http
GET /api/deepseek_service.php?action=stats
```

## Doubao API接口

### 基础URL
```
/api/doubao_service.php
```

Doubao API接口与DeepSeek API接口完全相同，只是服务名称不同。所有端点和参数都保持一致。

### 主要差异

1. **API密钥格式验证**：Doubao使用不同的密钥格式验证规则
2. **API端点**：使用 `https://ark.cn-beijing.volces.com/api/v3/chat/completions`
3. **可用模型**：
   - doubao-seed-1-6-250615
   - doubao-1.5-vision-pro-250328
   - doubao-seed-1-6-thinking-250715

## 统一验证API

### 基础URL
```
/api/ai_service_validator.php
```

### 1. 获取可用服务
```http
GET /api/ai_service_validator.php?action=services
```

### 2. 获取服务状态
```http
GET /api/ai_service_validator.php?action=status
```

### 3. 验证DeepSeek密钥
```http
POST /api/ai_service_validator.php
Content-Type: application/json

{
  "action": "validate_deepseek",
  "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

### 4. 验证Doubao密钥
```http
POST /api/ai_service_validator.php
Content-Type: application/json

{
  "action": "validate_doubao",
  "api_key": "your_doubao_api_key"
}
```

### 5. 批量验证密钥
```http
POST /api/ai_service_validator.php
Content-Type: application/json

{
  "action": "batch_validate",
  "keys": [
    {
      "service": "deepseek",
      "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    },
    {
      "service": "doubao",
      "api_key": "your_doubao_api_key"
    }
  ]
}
```

### 6. 测试所有服务
```http
POST /api/ai_service_validator.php
Content-Type: application/json

{
  "action": "test_all_services"
}
```

## 错误处理

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2025-01-17T10:30:00+00:00"
}
```

### 常见错误代码

- `INVALID_REQUEST` - 无效请求
- `MISSING_PARAMETER` - 缺少必需参数
- `INVALID_PARAMETER` - 参数无效
- `SERVICE_DISABLED` - 服务未启用
- `NO_VALID_API_KEY` - 没有可用的API密钥
- `API_KEY_INVALID` - API密钥无效
- `API_RATE_LIMIT` - API请求频率超限
- `API_NETWORK_ERROR` - 网络连接错误
- `API_TIMEOUT` - 请求超时
- `DATABASE_ERROR` - 数据库错误
- `INTERNAL_ERROR` - 服务器内部错误

## 安全特性

### 1. API密钥加密存储
- 使用AES-256-CBC加密算法
- 密钥哈希用于快速查找和验证
- 敏感信息在日志中自动脱敏

### 2. 请求验证
- 参数类型和格式验证
- SQL注入防护
- XSS攻击防护

### 3. 访问控制
- API访问常量验证
- 请求方法限制
- 错误信息过滤

### 4. 日志记录
- 完整的API请求日志
- 错误日志和警告日志
- 性能监控（响应时间）

## 部署说明

### 1. 数据库设置
```bash
# 访问设置脚本
http://your-domain/api/setup_ai_services.php?action=setup
```

### 2. 测试API
```bash
# 运行测试脚本
http://your-domain/api/test_ai_services.php?run_tests=1
```

### 3. 配置要求
- PHP 7.4+
- MySQL 5.7+
- cURL扩展
- OpenSSL扩展

### 4. 文件权限
```bash
chmod 755 /api/
chmod 644 /api/*.php
chmod 755 /logs/
```

## 使用示例

### JavaScript前端集成
```javascript
// 获取DeepSeek配置
async function getDeepSeekConfig() {
    const response = await fetch('/api/deepseek_service.php?action=config');
    const data = await response.json();
    return data;
}

// 添加API密钥
async function addApiKey(service, apiKey) {
    const response = await fetch(`/api/${service}_service.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'add_key',
            api_key: apiKey
        })
    });
    return await response.json();
}

// 切换服务状态
async function toggleService(service, enabled) {
    const response = await fetch(`/api/${service}_service.php`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'toggle',
            enabled: enabled
        })
    });
    return await response.json();
}
```

## 维护和监控

### 1. 日志清理
系统会自动清理30天前的日志，也可以手动调用清理功能。

### 2. 性能监控
- 响应时间统计
- 成功率统计
- API使用频率统计

### 3. 错误监控
- 实时错误日志
- 错误趋势分析
- 自动告警（可扩展）

## 扩展性

系统设计支持以下扩展：
1. 添加新的AI服务提供商
2. 自定义验证规则
3. 高级配置选项
4. 负载均衡和故障转移
5. 缓存机制
6. 监控和告警系统
