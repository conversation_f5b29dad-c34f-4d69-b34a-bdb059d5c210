-- AI服务配置数据库设置
-- 创建时间: 2025-01-17
-- 版本: 1.0.0

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ====================================
-- AI服务配置表结构
-- ====================================

-- AI服务配置主表
DROP TABLE IF EXISTS `ai_service_configs`;
CREATE TABLE `ai_service_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `service_name` varchar(50) NOT NULL COMMENT '服务名称 (deepseek, doubao)',
  `service_type` varchar(50) NOT NULL COMMENT '服务类型 (chat, completion)',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `api_endpoint` varchar(255) NOT NULL COMMENT 'API端点URL',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `system_prompt` text COMMENT '系统提示词',
  `reply_delay` int(11) NOT NULL DEFAULT 0 COMMENT '回复延迟（秒）',
  `thinking_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用深度思考',
  `max_tokens` int(11) NOT NULL DEFAULT 4000 COMMENT '最大令牌数',
  `temperature` decimal(3,2) NOT NULL DEFAULT 0.70 COMMENT '温度参数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_name` (`service_name`),
  KEY `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务配置表';

-- AI服务API密钥表
DROP TABLE IF EXISTS `ai_service_api_keys`;
CREATE TABLE `ai_service_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `service_name` varchar(50) NOT NULL COMMENT '服务名称 (deepseek, doubao)',
  `api_key` varchar(255) NOT NULL COMMENT 'API密钥（加密存储）',
  `api_key_hash` varchar(64) NOT NULL COMMENT 'API密钥哈希值（用于验证）',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `is_valid` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否验证有效',
  `last_validated_at` timestamp NULL DEFAULT NULL COMMENT '最后验证时间',
  `validation_error` text COMMENT '验证错误信息',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_key_hash` (`api_key_hash`),
  KEY `idx_service_name` (`service_name`),
  KEY `idx_active_valid` (`is_active`, `is_valid`),
  FOREIGN KEY (`service_name`) REFERENCES `ai_service_configs`(`service_name`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务API密钥表';

-- AI服务使用日志表
DROP TABLE IF EXISTS `ai_service_logs`;
CREATE TABLE `ai_service_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `service_name` varchar(50) NOT NULL COMMENT '服务名称',
  `api_key_id` int(11) DEFAULT NULL COMMENT 'API密钥ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型 (validate, chat, test)',
  `request_data` text COMMENT '请求数据（JSON）',
  `response_data` text COMMENT '响应数据（JSON）',
  `status` varchar(20) NOT NULL COMMENT '状态 (success, error)',
  `error_message` text COMMENT '错误信息',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间（毫秒）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_service_name` (`service_name`),
  KEY `idx_api_key_id` (`api_key_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`service_name`) REFERENCES `ai_service_configs`(`service_name`) ON DELETE CASCADE,
  FOREIGN KEY (`api_key_id`) REFERENCES `ai_service_api_keys`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务使用日志表';

-- ====================================
-- 默认数据插入
-- ====================================

-- 插入默认AI服务配置
INSERT INTO `ai_service_configs` (`service_name`, `service_type`, `is_enabled`, `api_endpoint`, `model_name`, `system_prompt`, `reply_delay`, `thinking_enabled`, `max_tokens`, `temperature`) VALUES
('deepseek', 'chat', 0, 'https://api.deepseek.com/chat/completions', 'deepseek-chat', '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。', 0, 0, 4000, 0.70),
('doubao', 'chat', 0, 'https://ark.cn-beijing.volces.com/api/v3/chat/completions', 'doubao-seed-1-6-250615', '你是一名专业的微信小店客服人员，请用自然、友好的语言回答用户问题，回答要口语化且不超过50字。注意：不要提及自己是机器人或AI助手，要表现出真人的服务态度。', 0, 0, 4000, 0.70)
ON DUPLICATE KEY UPDATE 
  `api_endpoint` = VALUES(`api_endpoint`),
  `model_name` = VALUES(`model_name`),
  `system_prompt` = VALUES(`system_prompt`),
  `updated_at` = CURRENT_TIMESTAMP;

SET FOREIGN_KEY_CHECKS = 1;
